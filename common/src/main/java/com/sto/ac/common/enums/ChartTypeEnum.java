package com.sto.ac.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ChartTypeEnum {

    LINE_CHART("LINE_CHART"),
    BAR_CHART("BAR_CHART"),
    PIE_CHART("PIE_CHART"),
    DOUGHNUT_CHART("DOUGHNUT_CHART"),
    TABLE_CHART("TABLE_CHART");

    private final String name;

    public String getStartTag() {
        return name + "_START";
    }

    public String getEndTag() {
        return name + "_END";
    }

    public String getName() {
        return name;
    }

}
