package com.sto.ac.api.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sto.ac.app.DialogueService;
import com.sto.ac.app.dto.DialogueRequestDTO;
import com.sto.ac.app.dto.MessagePartDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@RestController
@RequestMapping("/xihe")
public class XiheDialogueController {

    @Autowired
    private DialogueService dialogueService;

    /**
     * 羲和对话Agent
     * @param
     * @return SseEmitter
     */
    @PostMapping("/dialogue")
    public SseEmitter dialogue(@RequestBody DialogueRequestDTO dialogueRequestDTO) {
        SseEmitter emitter = new SseEmitter();
        String intentSessionId = null;
        String dataSessionId = null;
        String sessionId = dialogueRequestDTO.getSessionId();
        if (StringUtils.isNotBlank(sessionId)) {
            JSONObject jsonObject = JSON.parseObject(sessionId);
            if (Objects.nonNull(jsonObject)) {
                intentSessionId = jsonObject.getString("intentSessionId");
                dataSessionId = jsonObject.getString("dataSessionId");
            }
        }
        AtomicReference<String> query = new AtomicReference<>();
        String question = dialogueRequestDTO.getQuestion();

        if (StringUtils.isNotBlank(question)) {
            query.set(question);
        } else {
            dialogueRequestDTO.getMessageParts().stream().filter(p -> Objects.equals(p.getMessageTypeEnum(), "text"))
                              .findFirst()
                              .ifPresent(messagePartDTO -> query.set(messagePartDTO.getContent()));
        }
        log.info("XiheDialogueController#dialogue query={}", query.get());
        dialogueService.dialogue(query.get(), intentSessionId, dataSessionId, emitter);
        return emitter;
    }
    
    
}
