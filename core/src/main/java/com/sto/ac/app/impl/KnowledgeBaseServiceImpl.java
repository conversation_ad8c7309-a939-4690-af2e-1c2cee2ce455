package com.sto.ac.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.sto.ac.app.KnowledgeBaseService;
import com.sto.ac.app.dto.DialogueResponseDTO;
import com.sto.ac.app.dto.KnowledgeBaseRequestDTO;
import com.sto.ac.app.dto.KnowledgeBaseResponseDTO;
import com.sto.ac.app.dto.OutputDTO;
import com.sto.ac.common.util.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.List;

/**
 * 知识库服务实现类
 */
@Service
@Slf4j
public class KnowledgeBaseServiceImpl implements KnowledgeBaseService {

    @Value("${knowledge.base.url:https://baseinfo-chatbotapi-test.sto.cn/api/openness/custom/ask/stream}")
    private String knowledgeBaseUrl;

    @Value("${knowledge.base.apiKey:ak-oi87!@&HRYFU4KUey35xPqAq7vpyHedgYuezRjT4EYFNlEVeISt1K8K934ehdy}")
    private String apiKey;

    @Value("${knowledge.base.appName:KEFU}")
    private String appName;

    @Value("${knowledge.base.robotCode:J250319001}")
    private String robotCode;

    @Override
    public void queryKnowledgeBase(String query, String wtUserCode, String intentSessionId, SseEmitter emitter) {
        try {
            log.info("KnowledgeBaseServiceImpl#queryKnowledgeBase starting query: {}, wtUserCode: {}", query, wtUserCode);

            // 构建请求体
            KnowledgeBaseRequestDTO requestDTO = new KnowledgeBaseRequestDTO();
            requestDTO.setQuestion(query);
            requestDTO.setWtUserCode(wtUserCode);

            String requestBody = JSON.toJSONString(requestDTO);
            log.debug("KnowledgeBaseServiceImpl#queryKnowledgeBase request body: {}", requestBody);

            // 构建HTTP请求
            Request request = new Request.Builder()
                    .url(knowledgeBaseUrl)
                    .post(RequestBody.create(MediaType.parse("application/json"), requestBody))
                    .addHeader("Openness-Api-Key", apiKey)
                    .addHeader("Openness-Api-App-Name", appName)
                    .addHeader("robotCode", robotCode)
                    .addHeader("Content-Type", "application/json")
                    .build();

            // 执行请求
            Call call = HttpClientUtil.questionHttpClient.newCall(request);
            Response response = call.execute();

            if (!response.isSuccessful()) {
                log.error("KnowledgeBaseServiceImpl#queryKnowledgeBase HTTP request failed: {}", response.code());
                emitter.send(SseEmitter.event().data("知识库查询失败，HTTP状态码: " + response.code()).name("message"));
                emitter.complete();
                return;
            }

            // 处理流式响应
            ResponseBody responseBody = response.body();
            if (responseBody == null) {
                log.error("KnowledgeBaseServiceImpl#queryKnowledgeBase response body is null");
                emitter.send(SseEmitter.event().data("知识库查询失败，响应体为空").name("message"));
                emitter.complete();
                return;
            }
            // 读取SSE流式数据
            try (BufferedReader reader = new BufferedReader(responseBody.charStream())) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (StringUtils.isNotBlank(line) && line.startsWith("data:")) {
                        try {
                            KnowledgeBaseResponseDTO knowledgeBaseResponseDTO = JSON.parseObject(line.substring(5), KnowledgeBaseResponseDTO.class);
                            log.info("KnowledgeBaseServiceImpl#queryKnowledgeBase response: {}", JSON.toJSONString(knowledgeBaseResponseDTO));
                            if (knowledgeBaseResponseDTO != null && knowledgeBaseResponseDTO.isSuccess() && knowledgeBaseResponseDTO.getData() != null) {
                                KnowledgeBaseResponseDTO.DataDTO data = knowledgeBaseResponseDTO.getData();
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("intentSessionId", intentSessionId);
                                jsonObject.put("dataSessionId", null);
                                data.setSessionId(jsonObject.toJSONString());
                                // 检查是否完成（根据finishReason字段判断）
                                if ("stop".equals(data.getFinishReason())) {
                                    emitter.send(SseEmitter.event().data(knowledgeBaseResponseDTO).name("message"));
                                    break;
                                }
                                emitter.send(SseEmitter.event().data(knowledgeBaseResponseDTO).name("message"));
                            }
                        } catch (Exception e) {
                            log.error("KnowledgeBaseServiceImpl#queryKnowledgeBase error parsing line: {}", line, e);
                            emitter.send(SseEmitter.event().data("知识库查询失败，解析错误").name("message"));
                            emitter.completeWithError(e);
                        }
                    }

                }
                emitter.complete();
                
            } catch (IOException e) {
                log.error("KnowledgeBaseServiceImpl#queryKnowledgeBase error reading response stream", e);
                emitter.send(SseEmitter.event().data("知识库查询过程中发生错误").name("message"));
                emitter.completeWithError(e);
            } finally {
                response.close();
            }

        } catch (Exception e) {
            log.error("KnowledgeBaseServiceImpl#queryKnowledgeBase unexpected error for query: {}, wtUserCode: {}", 
                    query, wtUserCode, e);
            emitter.completeWithError(e);
        }
    }
} 
