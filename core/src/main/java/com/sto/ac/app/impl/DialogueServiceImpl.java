package com.sto.ac.app.impl;

import com.alibaba.dashscope.app.*;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sto.ac.app.DialogueService;
import com.sto.ac.app.KnowledgeBaseService;
import com.sto.ac.app.dto.*;
import com.sto.ac.common.base.UserInfoResp;
import com.sto.ac.common.enums.ChartTypeEnum;
import com.sto.ac.common.util.UserInfoHolder;
import io.reactivex.Flowable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Slf4j
public class DialogueServiceImpl implements DialogueService {

    @Value("${xihe.dialogue.apiKey:sk-29ab26f17d384119bdb62e1ecd678a18}")
    private String apiKey;

    @Value("${xihe.dialogue.data.appId:cdc0546b1cd8436c87eba9af8d02d305}")
    private String dataQueryAppId;

    @Value("${xihe.dialogue.intent.appId:a7149537bffd43bdbe3f60ce9603ac38}")
    private String intentAppId;

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    @Override
    public void dialogue(String query, String intentSessionId, String dataSessionId, SseEmitter emitter) {
        try {
            // 参数校验
            if (StringUtils.isBlank(query)) {
                log.warn("DialogueServiceImpl#dialogue query is blank");
                emitter.send(SseEmitter.event().data("查询内容不能为空").name("message"));
                emitter.complete();
                return;
            }

            // 先意图识别，使用intentSessionId
            IntentOutputDTO queryIntent = getQueryIntent(query, intentSessionId);
            log.info("DialogueServiceImpl#dialogue query={}, intentSessionId={}, dataSessionId={}, intent={}", 
                    query, intentSessionId, dataSessionId, JSON.toJSONString(queryIntent));
            
            if (Objects.isNull(queryIntent)) {
                log.error("DialogueServiceImpl#dialogue intent recognition failed for query={}", query);
                emitter.send(SseEmitter.event().data("意图识别失败，请稍后重试").name("message"));
                emitter.complete();
                return;
            }

            String intent = queryIntent.getIntent();
            String newIntentSessionId = queryIntent.getSessionId();
            
            // 根据意图进行不同的处理
            if ("2".equals(intent)) {
                // 意图为2：调用业务数据查询Agent（支持多轮对话），使用dataSessionId
                log.info("DialogueServiceImpl#dialogue calling data query agent for query={}, dataSessionId={}", query, dataSessionId);
                queryData(query, dataSessionId, newIntentSessionId, emitter);
            } else {
                // 意图为1或-1：调用外部知识库问答接口（不支持多轮对话）
                log.info("DialogueServiceImpl#dialogue calling knowledge base QA for query={}", query);
                queryQA(query, newIntentSessionId, emitter);
            }
        } catch (Exception e) {
            log.error("DialogueServiceImpl#dialogue unexpected error for query={}, intentSessionId={}, dataSessionId={}", 
                    query, intentSessionId, dataSessionId, e);
            try {
                emitter.send(SseEmitter.event().data("系统异常，请稍后重试"));
                emitter.completeWithError(e);
            } catch (Exception sendException) {
                log.error("DialogueServiceImpl#dialogue failed to send error message", sendException);
                emitter.completeWithError(e);
            }
        }
    }

    private void queryQA(String query, String intentSessionId, SseEmitter emitter) {
        try {
            log.info("DialogueServiceImpl#queryQA starting knowledge base query for: {}", query);
            
            UserInfoResp userInfo = UserInfoHolder.getUserInfo();
            String wtUserId = userInfo.getUserId();
            
            log.debug("DialogueServiceImpl#queryQA user info: wtUserId={}", wtUserId);
            
            // 调用知识库服务
            knowledgeBaseService.queryKnowledgeBase(query, wtUserId, intentSessionId, emitter);
            
        } catch (Exception e) {
            log.error("DialogueServiceImpl#queryQA error for query: {}", query, e);
            try {
                emitter.send(SseEmitter.event().data("知识库查询失败，系统异常").name("message"));
                emitter.completeWithError(e);
            } catch (Exception sendException) {
                log.error("DialogueServiceImpl#queryQA failed to send error message", sendException);
                emitter.completeWithError(e);
            }
        }
    }

    private void queryData(String query, String dataSessionId, String newIntentSessionId, SseEmitter emitter) {
        try {
            log.info("DialogueServiceImpl#queryData starting data query for: {}, sessionId: {}", query, dataSessionId);
            
            UserInfoResp userInfo = UserInfoHolder.getUserInfo();
            String wtUserId = userInfo.getUserId();
            
            log.debug("DialogueServiceImpl#queryData user info: wtUserId={}", wtUserId);

            ApplicationParam param = ApplicationParam.builder()
                                                     .apiKey(apiKey)
                                                     .appId(dataQueryAppId)
                                                     .prompt("工号" + wtUserId + "用户提问：" + query + "，严禁编造数据，必须使用MCP工具查询真实数据！")
//                                                     .flowStreamMode(FlowStreamMode.AGENT_FORMAT)
//                                                     .incrementalOutput(true)
                                                     .build();
            if (StringUtils.isNotBlank(dataSessionId)) {
                param.setSessionId(dataSessionId);
            }
            
            Application application = new Application();
            long startTime = System.currentTimeMillis();
            // 使用AtomicBoolean标记是否是首次响应，语义更清晰且线程安全
            AtomicBoolean isFirstResponse = new AtomicBoolean(true);
            Flowable<ApplicationResult> result = application.streamCall(param);

            result.blockingForEach(data -> {
                if (isFirstResponse.get()) {
                    log.info("DialogueServiceImpl#queryData 百炼应用首包响应耗时：{}ms", System.currentTimeMillis() - startTime);
                }
                ApplicationOutput output = data.getOutput();
                String text = output.getText();
                String newDataSessionId = output.getSessionId();
                String finishReason = output.getFinishReason();

                KnowledgeBaseResponseDTO knowledgeBaseResponseDTO = createKnowledgeBaseResponse(newIntentSessionId, newDataSessionId, text);

                emitter.send(SseEmitter.event().data(knowledgeBaseResponseDTO).name("message"));
                if (isFirstResponse.compareAndSet(true, false)) {
                    log.info("DialogueServiceImpl#queryData 发送给前端时间：{}，首包耗时：{}ms", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")), System.currentTimeMillis() - startTime);
                }
                if (Objects.equals("stop", finishReason)) {
                    emitter.complete();
                }
            });
            
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            emitter.completeWithError(e);
        }
    }


    /**
     * 创建知识库响应DTO
     */
    private KnowledgeBaseResponseDTO createKnowledgeBaseResponse(String intentSessionId, String dataSessionId, String text) {
        KnowledgeBaseResponseDTO response = new KnowledgeBaseResponseDTO();
        KnowledgeBaseResponseDTO.DataDTO dataDTO = new KnowledgeBaseResponseDTO.DataDTO();
        List<OutputDTO> outputList = parseTextWithCharts(text);
        dataDTO.setOutputList(outputList);
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("intentSessionId", intentSessionId);
        jsonObject.put("dataSessionId", dataSessionId);
        dataDTO.setSessionId(jsonObject.toJSONString());
        response.setData(dataDTO);
        return response;
    }

    /**
     * 解析包含图表标记的文本
     * @param text 包含图表标记的文本
     * @return 解析后的输出列表
     */
    private List<OutputDTO> parseTextWithCharts(String text) {
        List<OutputDTO> outputList = new ArrayList<>();

        // 1. 检测图表类型
        ChartTypeEnum chartTypeEnum = detectEndTag(text);
        if (Objects.isNull(chartTypeEnum)) {
            outputList.add(createTextOutput(text));
            return outputList;
        }

        // 2. 提取图表数据和前置文本
        ChartParseContext context = extractChartContext(text, chartTypeEnum);

        // 3. 添加前置文本（如果存在）
        if (StringUtils.isNotBlank(context.getPrefixText())) {
            outputList.add(createTextOutput(context.getPrefixText()));
        }

        // 4. 根据图表类型解析数据
        OutputDTO chartOutput = parseChartByType(chartTypeEnum, context.getChartData());
        if (Objects.nonNull(chartOutput)) {
            outputList.add(chartOutput);
        }

        // 5. 添加后置文本（如果存在）
        if (StringUtils.isNotBlank(context.getSuffixText())) {
            outputList.add(createTextOutput(context.getSuffixText()));
        }

        return outputList;
    }

    /**
     * 提取图表上下文信息
     */
    private ChartParseContext extractChartContext(String text, ChartTypeEnum chartType) {
        String startTag = chartType.getStartTag();
        String endTag = chartType.getEndTag();

        int startIndex = text.indexOf(startTag);
        int endIndex = text.indexOf(endTag);

        String prefixText = startIndex > 0 ? text.substring(0, startIndex) : "";
        String chartData = text.substring(startIndex + startTag.length(), endIndex).trim();
        String suffixText = endIndex + endTag.length() < text.length() ?
            text.substring(endIndex + endTag.length()) : "";

        return new ChartParseContext(prefixText, chartData, suffixText);
    }

    /**
     * 根据图表类型解析数据
     */
    private OutputDTO parseChartByType(ChartTypeEnum chartType, String chartData) {
        switch (chartType) {
            case TABLE_CHART:
                return parseTableChart(chartData);
            case LINE_CHART:
                return parseLineOrBarChart(chartData, ChartTypeEnum.LINE_CHART);
            case BAR_CHART:
                return parseLineOrBarChart(chartData, ChartTypeEnum.BAR_CHART);
            default:
                return createTextOutput(chartData);
        }
    }

    /**
     * 解析表格图表
     */
    private OutputDTO parseTableChart(String chartData) {
        JSONObject result = new JSONObject();
        String[] parts = chartData.split(";");

        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("d[")) {
                result.put("dataSource", parseDataSource(part));
            } else if (part.startsWith("c[")) {
                result.put("columns", parseColumns(part));
            }
        }

        return createChartOutput("TABLE_CHART", result.toJSONString());
    }

    /**
     * 解析折线图
     */
    private OutputDTO parseLineOrBarChart(String chartData, ChartTypeEnum chartType) {
        JSONObject result = new JSONObject();
        String[] parts = chartData.split(";");

        JSONObject mapConfig = new JSONObject();

        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("d[")) {
                result.put("dataSource", parseDataSource(part));
            } else if (part.startsWith("x[")) {
                mapConfig.put("xAxis", parseXAxis(part));
            } else if (part.startsWith("s[")) {
                mapConfig.put("series", parseSeries(part));
            } else if (part.startsWith("y[")) {
                mapConfig.put("yAxis", parseYAxis(part));
            }
        }

        result.put("mapConfig", mapConfig);
        result.put("height", 300);
        if (Objects.equals(chartType, ChartTypeEnum.LINE_CHART)) {
            result.put("hideCheckbox", false);
            result.put("tooltipTheme", "light");
        }
        return createChartOutput(chartType.getName(), result.toJSONString());
    }

    /**
     * 解析X轴配置
     * @param xPart 格式如：x[field:date]
     * @return X轴配置JSON对象
     */
    private JSONObject parseXAxis(String xPart) {
        JSONObject xAxis = new JSONObject();
        String content = xPart.replaceAll("x\\[|\\]", "");
        String[] properties = content.split(",");

        for (String property : properties) {
            String[] keyValue = property.split(":");
            if (keyValue.length == 2) {
                xAxis.put(keyValue[0], keyValue[1]);
            }
        }

        return xAxis;
    }

    /**
     * 解析系列配置
     * @param sPart 格式如：s[field:completeRate,name:完结率,type:line,unit:%]
     * @return 系列配置JSON数组
     */
    private JSONArray parseSeries(String sPart) {
        JSONArray series = new JSONArray();
        String content = sPart.replaceAll("s\\[|\\]", "");
        String[] items = content.split("\\\\");

        for (String item : items) {
            JSONObject seriesItem = new JSONObject();
            String[] properties = item.split(",");

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    seriesItem.put(keyValue[0], keyValue[1]);
                }
            }

            if (!seriesItem.isEmpty()) {
                series.add(seriesItem);
            }
        }

        return series;
    }

    /**
     * 解析Y轴配置
     * @param yPart 格式如：y[name:完结率]
     * @return Y轴配置JSON数组
     */
    private JSONArray parseYAxis(String yPart) {
        JSONArray yAxis = new JSONArray();
        String content = yPart.replaceAll("y\\[|\\]", "");
        String[] items = content.split("\\\\");

        for (String item : items) {
            JSONObject yAxisItem = new JSONObject();
            String[] properties = item.split(",");

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    yAxisItem.put(keyValue[0], keyValue[1]);
                }
            }

            if (!yAxisItem.isEmpty()) {
                yAxis.add(yAxisItem);
            }
        }

        return yAxis;
    }


    private JSONArray parseDataSource(String dataPart) {
        String content = dataPart.replaceAll("d\\[|\\]", "");
        String[] items = content.split("\\\\");
        JSONArray dataSource = new JSONArray();

        for (String item : items) {
            String[] properties = item.split(",");
            JSONObject dataItem = new JSONObject();

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    dataItem.put(keyValue[0], keyValue[1]);
                }
            }

            if (!dataItem.isEmpty()) {
                dataSource.add(dataItem);
            }
        }

        return dataSource;
    }

    private JSONArray parseColumns(String columnPart) {
        String content = columnPart.replaceAll("c\\[|\\]", "");
        String[] items = content.split("\\\\");
        JSONArray columns = new JSONArray();

        for (String item : items) {
            String[] properties = item.split(",");
            JSONObject column = new JSONObject();

            for (String property : properties) {
                String[] keyValue = property.split(":");
                if (keyValue.length == 2) {
                    column.put(keyValue[0], keyValue[1]);
                }
            }

            if (!column.isEmpty()) {
                columns.add(column);
            }
        }

        return columns;
    }

    /**
     * 检测文本中的图表类型
     */
    private ChartTypeEnum detectChartType(String text) {
        for (ChartTypeEnum chartType : ChartTypeEnum.values()) {
            if (text.contains(chartType.getStartTag())) {
                return chartType;
            }
        }
        return null;
    }

    private ChartTypeEnum detectEndTag(String text) {
        for (ChartTypeEnum chartType : ChartTypeEnum.values()) {
            if (text.contains(chartType.getEndTag())) {
                return chartType;
            }
        }
        return null;
    }

    /**
     * 创建文本输出DTO
     */
    private OutputDTO createTextOutput(String content) {
        OutputDTO outputDTO = new OutputDTO();
        outputDTO.setType("TEXT");
        outputDTO.setContent(content);
        return outputDTO;
    }

    /**
     * 创建图表输出DTO
     */
    private OutputDTO createChartOutput(String chartType, String content) {
        OutputDTO outputDTO = new OutputDTO();
        outputDTO.setType(chartType);
        outputDTO.setContent(content);
        return outputDTO;
    }

    private IntentOutputDTO getQueryIntent(String query, String sessionId) {
        try {
            log.info("DialogueServiceImpl#getQueryIntent starting intent recognition for: {}, sessionId: {}", query, sessionId);
            
            ApplicationParam param = ApplicationParam.builder()
                                                     .apiKey(apiKey)
                                                     .appId(intentAppId)
                                                     .prompt(query)
                                                     .build();
            if (StringUtils.isNotBlank(sessionId)) {
                param.setSessionId(sessionId);
            }
            
            Application application = new Application();
            ApplicationResult result = application.call(param);
            
            String intent = result.getOutput().getText();
            String resultSessionId = result.getOutput().getSessionId();

            IntentOutputDTO intentOutputDTO = new IntentOutputDTO();
            intentOutputDTO.setIntent(intent);
            intentOutputDTO.setSessionId(resultSessionId);
            
            return intentOutputDTO;
        } catch (ApiException | NoApiKeyException | InputRequiredException e) {
            log.error("DialogueServiceImpl#getQueryIntent 调用百炼羲和意图识别Agent失败 query={}, sessionId={}: {}", query, sessionId, e.getMessage(), e);
            return null;
        } catch (Exception e) {
            log.error("DialogueServiceImpl#getQueryIntent unexpected error for query={}, sessionId={}", query, sessionId, e);
            return null;
        }
    }
}
