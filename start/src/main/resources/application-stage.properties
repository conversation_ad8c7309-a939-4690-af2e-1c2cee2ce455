server.port = 7001
spring.application.name = customer-ai-center

#apollo
app.id=customer-ai-center
apollo.bootstrap.enabled=true
apollo.meta=http://10.36.30.90:8082
spring.apollo.test=true

knowledge.base.url=https://pre-baseinfo-chatbotapi.sto.cn/api/openness/custom/ask/stream
knowledge.base.apiKey=ak-oi87!@&HRYFU4KUey35xPqAq7vpyHedgYuezRjT4EYFNlEVeISt1K8K934ehdy
knowledge.base.appName=KEFU
knowledge.base.robotCode=J250319002

workorder.condition.test.url=https://fwzl-tickets-test.sto.cn/workOrderQuery/integrate/queryByCondition
workorder.condition.pro.url=https://fwzl-tickets.sto.cn/workOrderQuery/integrate/queryByCondition
workorder.appKey=arthur